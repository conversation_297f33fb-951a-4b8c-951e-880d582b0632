import React, { useCallback } from "react"
import { FlatList, StyleSheet, ListRenderItem, KeyboardAvoidingView, Platform } from "react-native"
import { LoanDetailSectionItem } from "./types"
import SectionRenderer from "./components/SectionRenderer"
import { Background, EmptyView } from "@/components"
import { useTranslation } from "react-i18next"
import { useLoanDetailContext } from "./context/LoanDetailContext"
import LoanDetailHeader from "./components/LoanHeaderView"

const LoanDetailContent: React.FC = () => {
  const { t } = useTranslation()
  const { loanDetailError, sections, isLoadingLoanDetail } = useLoanDetailContext()

  const renderItem: ListRenderItem<LoanDetailSectionItem> = useCallback(({ item }) => {
    return <SectionRenderer item={item} />
  }, [])

  const keyExtractor = useCallback((item: LoanDetailSectionItem) => item.id, [])

  if (loanDetailError && !isLoadingLoanDetail) {
    return <EmptyView subtitle={t("Failed to load loan details")} />
  }

  if ((!sections || sections.length === 0) && !isLoadingLoanDetail) {
    return <EmptyView subtitle={t("No loan details found")} />
  }

  return (
    <FlatList
      data={sections}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
      keyboardShouldPersistTaps="handled"
      keyboardDismissMode="on-drag"
    />
  )
}

const LoanDetailView = () => {
  const { loan } = useLoanDetailContext()

  return (
    <Background>
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <LoanDetailHeader state={loan?.state} />
        <LoanDetailContent />
      </KeyboardAvoidingView>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 50,
  },
})

export default LoanDetailView
