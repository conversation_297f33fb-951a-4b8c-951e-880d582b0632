import React from "react"
import { useTranslation } from "react-i18next"
import { useAccount, useWriteContract } from "wagmi"
import { parseEther } from "@ethersproject/units"
import { maxUint256 } from "viem"
import { MortgageLoan } from "@/service/types"
import { mortgageTokenAbi } from "@/contracts/mortgage-token"
import { erc20Abi, useErc20Allowance } from "@/contracts"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import Logger from "src/utils/logger"
import useLoadingStore from "@/stores/loadingStore"
import { Keyboard } from "react-native"
import { PrimaryButton } from "@/components/Button"
import { CustomPressable } from "@/components"
import Colors from "@/config/colors"

const logger = new Logger({ tag: "RepayLoanButton" })

interface RepayLoanButtonProps {
  loan: MortgageLoan
  buttonContent: React.ReactNode
  onRefresh?: () => void
}

const RepayLoanButton: React.FC<RepayLoanButtonProps> = ({ loan, buttonContent, onRefresh }) => {
  const { t } = useTranslation()
  const { isLoading, setLoading } = useLoadingStore()

  const { address } = useAccount()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const currencyAllowanceWei = useErc20Allowance(
    address as string,
    loan.currency as `0x${string}`,
    CONTRACT_ADDRESS_MORTGAGE_TOKEN,
  )

  const handleRepay = async () => {
    if (isLoading || !ethersProvider) return

    // Dismiss keyboard before starting transaction
    Keyboard.dismiss()

    setLoading(true)
    try {
      if (currencyAllowanceWei < BigInt(parseEther(loan.repayment).toString())) {
        const txHash = await writeContractAsync({
          address: loan.currency as `0x${string}`,
          abi: erc20Abi,
          functionName: "approve",
          args: [CONTRACT_ADDRESS_MORTGAGE_TOKEN, maxUint256],
        })
        const receipt = await ethersProvider.waitForTransaction(txHash)
        if (receipt.status !== 1) {
          showError(t("Failed to approve"))
          throw new Error("Fail to approve currency")
        }
      }
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "repay",
        args: [BigInt(loan.id)],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Repay success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        showError(t("Repay failed"))
      }
    } catch (e) {
      showError(t("Repay failed"))
      logger.error("Failed to repay loan", {
        component: "TokenizationLoansView",
        action: "Repay",
        error: e,
      })
    } finally {
      setLoading(false)
    }
  }

  if (!address) {
    return null
  }

  return buttonContent ? (
    <CustomPressable onPress={handleRepay} dismissKeyboard={true}>
      {buttonContent}
    </CustomPressable>
  ) : (
    <PrimaryButton title={t("Repay")} onPress={handleRepay} color={Colors.Danger500} />
  )
}

export { RepayLoanButton }
