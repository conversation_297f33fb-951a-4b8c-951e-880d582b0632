import React, { ReactNode } from "react"
import { Pressable, StyleProp, View, ViewStyle, Keyboard } from "react-native"

interface CustomPressableProps {
  onPress?: () => void
  children: ReactNode
  enabled?: boolean
  style?: StyleProp<ViewStyle>
  scale?: number
  dismissKeyboard?: boolean
}

export const CustomPressable: React.FC<CustomPressableProps> = ({
  onPress,
  children,
  enabled = true,
  style,
  scale = 1.02,
  dismissKeyboard = false,
}) => {
  const handlePress = () => {
    if (dismissKeyboard) {
      Keyboard.dismiss()
    }
    onPress?.()
  }
  const renderContent = () => {
    if (!enabled) {
      return <View style={style}>{children}</View>
    }

    return (
      <Pressable
        onPress={handlePress}
        style={({ pressed }) => [
          {
            transform: [{ scale: pressed ? scale : 1 }],
          },
          style,
        ]}
      >
        {children}
      </Pressable>
    )
  }

  return renderContent()
}
