import { useQuery } from "@tanstack/react-query"
import React, { useState } from "react"
import { ActivityIndicator, FlatList, StyleProp, StyleSheet, View, ViewStyle } from "react-native"
import { ListResponse } from "@/service/types"
import FooterPaging from "./FooterPaging"
import { EmptyView } from "../EmptyView"
import { useIsFocused } from "@react-navigation/native"
import Colors from "@/config/colors"

interface Props<T> {
  getData: (...args: any[]) => Promise<ListResponse<T>>
  renderItem: (item: T) => React.ReactElement | null
  keyExtractor?: (item: T, index: number) => string
  scrollEnabled?: boolean
  queryKeys?: (string | undefined)[]
  initialPage?: number
  pageSize?: number
  emptyTitle?: string
  emptyMessage?: string
  numColumns?: number
  columnWrapperStyle?: StyleProp<ViewStyle>
  contentContainerStyle?: StyleProp<ViewStyle>
  defaultHeight?: number
}

function SimplePagingList<T>({
  getData,
  renderItem,
  keyExtractor,
  scrollEnabled = true,
  queryKeys,
  initialPage = 1,
  pageSize = 10,
  emptyTitle,
  emptyMessage,
  columnWrapperStyle,
  contentContainerStyle,
  numColumns,
  defaultHeight,
}: Props<T>): React.ReactElement {
  const isFocused = useIsFocused()

  const [currentPage, setCurrentPage] = useState<number>(initialPage)
  const params = {
    currentPage: currentPage,
    itemsPerPage: pageSize,
  }

  const {
    data = {
      list: [],
      pagination: {
        itemsPerPage: 10,
        currentPage: 1,
        totalItems: 0,
      },
    },
    isLoading,
  } = useQuery({
    queryKey: [...(queryKeys || []), currentPage, pageSize],
    queryFn: () => getData(params),
    enabled: isFocused,
  })

  const { list, pagination } = data

  const handleNextPage = () => {
    const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage)
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1)
    }
  }

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1)
    }
  }

  const renderFooter = () => {
    if (list.length === 0) {
      return null
    }
    const totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage)
    return (
      <FooterPaging
        itemsOnPage={list.length}
        currentPage={currentPage}
        total={pagination.totalItems}
        totalPages={totalPages}
        onNextPage={handleNextPage}
        onPrevPage={handlePrevPage}
      />
    )
  }

  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { height: defaultHeight }]}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
      </View>
    )
  }

  if (list.length === 0 && !isLoading) {
    return <EmptyView title={emptyTitle} subtitle={emptyMessage} />
  }

  return (
    <>
      <FlatList
        style={styles.list}
        data={list}
        renderItem={({ item }) => renderItem(item)}
        keyExtractor={keyExtractor}
        scrollEnabled={scrollEnabled}
        numColumns={numColumns}
        columnWrapperStyle={columnWrapperStyle}
        contentContainerStyle={contentContainerStyle}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
      />
      {renderFooter()}
    </>
  )
}

export default SimplePagingList

const styles = StyleSheet.create({
  list: {
    marginBottom: 16,
  },
  loadingContainer: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
  },
})
